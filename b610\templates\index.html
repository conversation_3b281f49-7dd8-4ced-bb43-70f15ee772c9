<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚假评论检测系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --genuine-color: #06d6a0;
            --fake-color: #ef476f;
            --gray-color: #6c757d;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--light-color);
            padding: 0;
            margin: 0;
        }
        
        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .header-content {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .header-title {
            flex-grow: 1;
            text-align: center;
        }
        
        .logout-btn {
            position: absolute;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
            text-decoration: none;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .review-card {
            background-color: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        h1 {
            font-weight: 500;
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        
        h2 {
            font-weight: 400;
            font-size: 1.25rem;
            color: var(--gray-color);
            margin-bottom: 1.5rem;
        }
        
        .review-textarea {
            width: 100%;
            height: 180px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 1rem;
            resize: none;
        }
        
        .review-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }
        
        .btn-group {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
        }
        
        .btn-secondary {
            background-color: #e9ecef;
            color: var(--dark-color);
        }
        
        .btn-secondary:hover {
            background-color: #dde2e6;
        }
        
        .result-card {
            display: none;
            margin-top: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .result-genuine {
            background-color: rgba(6, 214, 160, 0.1);
            border: 1px solid var(--genuine-color);
        }
        
        .result-fake {
            background-color: rgba(239, 71, 111, 0.1);
            border: 1px solid var(--fake-color);
        }
        
        .result-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .genuine-icon {
            color: var(--genuine-color);
        }
        
        .fake-icon {
            color: var(--fake-color);
        }
        
        .result-title {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .genuine-title {
            color: var(--genuine-color);
        }
        
        .fake-title {
            color: var(--fake-color);
        }
        
        .result-score {
            font-size: 1.1rem;
            color: var(--gray-color);
            margin-bottom: 1rem;
        }
        
        .loader {
            display: none;
            text-align: center;
            margin: 2rem 0;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .sample-info {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 0.9rem;
            color: var(--gray-color);
            display: none;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 1rem;
            top: 1rem;
        }

        .close:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }

        /* 酒店评论显示区域 */
        .hotel-comments-section {
            display: none;
            margin-top: 2rem;
        }

        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .comment-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--primary-color);
        }

        .comment-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: var(--gray-color);
        }

        .comment-content {
            line-height: 1.6;
            color: var(--dark-color);
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="header-title">
                <h1>虚假评论检测系统</h1>
                <h2>基于双层LSTM和GloVe词向量的深度学习模型</h2>
            </div>
        </div>
        <a href="/logout" class="logout-btn">退出登录</a>
    </div>
    
    <div class="container">
        <div class="review-card">
            <h2>输入评论文本或选择测试样本</h2>
            <textarea id="reviewTextarea" class="review-textarea" placeholder="在此输入评论文本..."></textarea>
            
            <div class="btn-group">
                <button id="loadSampleBtn" class="btn btn-secondary">加载测试样本</button>
                <button id="crawlHotelBtn" class="btn btn-secondary">爬取酒店评论</button>
                <button id="analyzeBtn" class="btn btn-primary">分析评论</button>
            </div>
            
            <div id="sampleInfo" class="sample-info">
                <p><strong>样本来源：</strong><span id="sampleSource"></span></p>
            </div>
        </div>
        
        <div id="loader" class="loader">
            <div class="spinner"></div>
            <p>正在分析评论...</p>
        </div>
        
        <div id="resultGenuine" class="result-card result-genuine">
            <div class="result-icon genuine-icon">✓</div>
            <h3 class="result-title genuine-title">真实评论</h3>
            <p class="result-score" id="genuineScore"></p>
            <p>系统认为这是一条真实评论。这条评论的语言模式、情感表达和内容结构符合真实用户的评论特征。</p>
        </div>
        
        <div id="resultFake" class="result-card result-fake">
            <div class="result-icon fake-icon">✗</div>
            <h3 class="result-title fake-title">虚假评论</h3>
            <p class="result-score" id="fakeScore"></p>
            <p>系统认为这是一条虚假评论。这条评论的语言模式、情感表达或内容结构显示出虚假评论的特征。</p>
        </div>

        <!-- 酒店评论显示区域 -->
        <div id="hotelCommentsSection" class="hotel-comments-section review-card">
            <div class="comments-header">
                <h2 id="hotelName">酒店评论</h2>
                <button id="hideCommentsBtn" class="btn btn-small btn-secondary">隐藏评论</button>
            </div>
            <div id="commentsContainer">
                <!-- 评论将在这里动态加载 -->
            </div>
        </div>
    </div>

    <!-- 爬取酒店评论模态框 -->
    <div id="crawlModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>爬取酒店评论</h2>
            <form id="crawlForm">
                <div class="form-group">
                    <label for="hotelNameInput" class="form-label">酒店名称</label>
                    <input type="text" id="hotelNameInput" class="form-control" placeholder="请输入酒店名称..." required>
                </div>
                <div class="form-group">
                    <label for="pageCountInput" class="form-label">爬取页数</label>
                    <input type="number" id="pageCountInput" class="form-control" value="1" min="1" max="5">
                </div>
                <div class="btn-group">
                    <button type="button" id="cancelCrawlBtn" class="btn btn-secondary">取消</button>
                    <button type="submit" class="btn btn-primary">开始爬取</button>
                </div>
            </form>
            <div id="crawlProgress" style="display: none; margin-top: 1rem;">
                <div class="spinner"></div>
                <p>正在爬取评论数据，请稍候...</p>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const reviewTextarea = document.getElementById('reviewTextarea');
            const loadSampleBtn = document.getElementById('loadSampleBtn');
            const crawlHotelBtn = document.getElementById('crawlHotelBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const loader = document.getElementById('loader');
            const resultGenuine = document.getElementById('resultGenuine');
            const resultFake = document.getElementById('resultFake');
            const genuineScore = document.getElementById('genuineScore');
            const fakeScore = document.getElementById('fakeScore');
            const sampleInfo = document.getElementById('sampleInfo');
            const sampleSource = document.getElementById('sampleSource');

            // 模态框相关元素
            const crawlModal = document.getElementById('crawlModal');
            const closeModal = document.querySelector('.close');
            const crawlForm = document.getElementById('crawlForm');
            const cancelCrawlBtn = document.getElementById('cancelCrawlBtn');
            const crawlProgress = document.getElementById('crawlProgress');

            // 酒店评论相关元素
            const hotelCommentsSection = document.getElementById('hotelCommentsSection');
            const hotelName = document.getElementById('hotelName');
            const commentsContainer = document.getElementById('commentsContainer');
            const hideCommentsBtn = document.getElementById('hideCommentsBtn');
            
            // 模态框控制
            crawlHotelBtn.addEventListener('click', function() {
                crawlModal.style.display = 'block';
            });

            closeModal.addEventListener('click', function() {
                crawlModal.style.display = 'none';
            });

            cancelCrawlBtn.addEventListener('click', function() {
                crawlModal.style.display = 'none';
            });

            window.addEventListener('click', function(event) {
                if (event.target === crawlModal) {
                    crawlModal.style.display = 'none';
                }
            });

            // 隐藏评论按钮
            hideCommentsBtn.addEventListener('click', function() {
                hotelCommentsSection.style.display = 'none';
            });

            // 加载测试样本
            loadSampleBtn.addEventListener('click', function() {
                loader.style.display = 'block';
                resultGenuine.style.display = 'none';
                resultFake.style.display = 'none';

                fetch('/get_sample')
                    .then(response => response.json())
                    .then(data => {
                        loader.style.display = 'none';
                        reviewTextarea.value = data.text;
                        sampleSource.textContent = data.actual_label;
                        sampleInfo.style.display = 'block';
                    })
                    .catch(error => {
                        loader.style.display = 'none';
                        alert('加载样本失败: ' + error);
                    });
            });
            
            // 分析评论
            analyzeBtn.addEventListener('click', function() {
                const reviewText = reviewTextarea.value.trim();
                
                if (!reviewText) {
                    alert('请输入评论文本或加载测试样本');
                    return;
                }
                
                // 显示加载动画
                loader.style.display = 'block';
                resultGenuine.style.display = 'none';
                resultFake.style.display = 'none';
                
                // 发送请求到后端
                fetch('/detect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ review: reviewText })
                })
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载动画
                    loader.style.display = 'none';
                    
                    // 显示结果
                    if (data.is_fake) {
                        fakeScore.textContent = `置信度: ${(data.probability * 100).toFixed(2)}%`;
                        resultFake.style.display = 'block';
                    } else {
                        genuineScore.textContent = `置信度: ${((1 - data.probability) * 100).toFixed(2)}%`;
                        resultGenuine.style.display = 'block';
                    }
                })
                .catch(error => {
                    loader.style.display = 'none';
                    alert('分析评论失败: ' + error);
                });
            });

            // 爬取酒店评论表单提交
            crawlForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const hotelNameValue = document.getElementById('hotelNameInput').value.trim();
                const pageCount = document.getElementById('pageCountInput').value;

                if (!hotelNameValue) {
                    alert('请输入酒店名称');
                    return;
                }

                // 显示进度
                crawlProgress.style.display = 'block';

                // 发送爬取请求
                fetch('/crawl_hotel_comments', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        hotel_name: hotelNameValue,
                        page_count: parseInt(pageCount)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    crawlProgress.style.display = 'none';

                    if (data.success) {
                        alert(`成功爬取 ${data.comments_count} 条评论！`);
                        crawlModal.style.display = 'none';

                        // 显示评论
                        displayHotelComments(data.hotel_name, data.comments);
                    } else {
                        alert('爬取失败: ' + data.message);
                    }
                })
                .catch(error => {
                    crawlProgress.style.display = 'none';
                    alert('爬取过程中发生错误: ' + error);
                });
            });

            // 显示酒店评论
            function displayHotelComments(hotelNameValue, comments) {
                hotelName.textContent = `${hotelNameValue} - 评论列表`;
                commentsContainer.innerHTML = '';

                if (comments && comments.length > 0) {
                    comments.forEach((comment, index) => {
                        const commentDiv = document.createElement('div');
                        commentDiv.className = 'comment-item';
                        commentDiv.innerHTML = `
                            <div class="comment-meta">
                                <span><strong>用户:</strong> ${comment.user_name || '匿名用户'}</span>
                                <span><strong>评论 ${index + 1}</strong></span>
                            </div>
                            <div class="comment-content">${comment.content || '暂无评论内容'}</div>
                        `;
                        commentsContainer.appendChild(commentDiv);
                    });
                } else {
                    commentsContainer.innerHTML = '<p>暂无评论数据</p>';
                }

                hotelCommentsSection.style.display = 'block';
            }
        });
    </script>
</body>
</html> 