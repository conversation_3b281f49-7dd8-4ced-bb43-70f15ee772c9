from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import torch
import pandas as pd
import numpy as np
import os
import json
import random
import mysql.connector 
from mysql.connector import Error
import hashlib
from functools import wraps
import datetime

from model import FakeReviewDetector
from preprocess import Preprocessor
from utils import predict

app = Flask(__name__)
app.secret_key = 'paj8sjd0a9sd0asdjaslkdjalsjd0a9wje09'  # 用于会话管理，应使用随机生成的强密钥

# 数据库配置
DB_CONFIG = {
    'host': 'dbconn.sealosbja.site',
    'port': 37895,
    'user': 'root',
    'password': 'rbfnpf2q',
    'database': 'fake_review_detection'
}

# Global variables
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = None
preprocessor = None
test_samples = None

def load_model():
    """Load the trained model and preprocessor"""
    global model, preprocessor
    
    # Load preprocessor
    preprocessor = Preprocessor.load('preprocessor.pkl')
    
    # Define model parameters
    vocab_size = len(preprocessor.word_to_idx)
    embedding_dim = preprocessor.embeddings.shape[1]
    hidden_dim = 256
    output_dim = 2  # Binary classification
    n_layers = 2
    dropout = 0.5
    pad_idx = preprocessor.word_to_idx['<PAD>']
    
    # Create model
    model = FakeReviewDetector(
        vocab_size=vocab_size,
        embedding_dim=embedding_dim,
        hidden_dim=hidden_dim,
        output_dim=output_dim,
        n_layers=n_layers,
        dropout=dropout,
        pad_idx=pad_idx,
        pretrained_embeddings=torch.FloatTensor(preprocessor.embeddings)
    ).to(device)
    
    # Load trained model weights
    model.load_state_dict(torch.load('best_model.pt', map_location=device))
    model.eval()

def load_test_samples():
    """Load some test samples from the OpSpam dataset"""
    global test_samples
    
    base_path = 'data/op_spam_v1.4'
    samples = []
    
    # Load some genuine reviews
    truthful_pos_path = os.path.join(base_path, 'positive_polarity', 'truthful_from_TripAdvisor')
    folders = os.listdir(truthful_pos_path)[:2]  # Just use a few folders
    for folder in folders:
        folder_path = os.path.join(truthful_pos_path, folder)
        if os.path.isdir(folder_path):
            files = os.listdir(folder_path)[:5]  # Take 5 samples from each folder
            for filename in files:
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    samples.append({
                        'text': f.read().strip(),
                        'actual_label': 'GENUINE'
                    })
    
    # Load some fake reviews
    deceptive_pos_path = os.path.join(base_path, 'positive_polarity', 'deceptive_from_MTurk')
    folders = os.listdir(deceptive_pos_path)[:2]  # Just use a few folders
    for folder in folders:
        folder_path = os.path.join(deceptive_pos_path, folder)
        if os.path.isdir(folder_path):
            files = os.listdir(folder_path)[:5]  # Take 5 samples from each folder
            for filename in files:
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='latin-1') as f:
                    samples.append({
                        'text': f.read().strip(),
                        'actual_label': 'FAKE'
                    })
    
    test_samples = samples
    return samples

# 认证相关函数
def hash_password(password, salt=None):
    """使用SHA-256和随机盐值加密密码"""
    if salt is None:
        salt = os.urandom(16).hex()  # 生成32字节的随机盐值
        
    # 组合密码和盐值
    salted_password = password + salt
    
    # 使用SHA-256加密
    hashed = hashlib.sha256(salted_password.encode()).hexdigest()
    
    return hashed, salt

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None

def login_required(f):
    """确保用户已登录的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
@login_required
def index():
    """主页，需要登录才能访问"""
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'GET':
        # 如果用户已登录，重定向到主页
        if 'user_id' in session:
            return redirect(url_for('index'))
        return render_template('login.html')
    
    # 处理POST请求（AJAX登录）
    if request.content_type == 'application/json':
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
    else:
        # 处理传统表单提交
        username = request.form.get('username')
        password = request.form.get('password')
    
    if not username or not password:
        return jsonify({'success': False, 'message': '用户名和密码不能为空'})
    
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '服务器错误，请稍后再试'})
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        # 查询用户
        cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
        user = cursor.fetchone()
        
        if not user:
            return jsonify({'success': False, 'message': '用户名或密码错误'})
        
        # 验证密码
        hashed_password, _ = hash_password(password, user['salt'])
        if hashed_password != user['password_hash']:
            return jsonify({'success': False, 'message': '用户名或密码错误'})
        
        # 更新最后登录时间
        cursor.execute("UPDATE users SET last_login = %s WHERE id = %s", 
                      (datetime.datetime.now(), user['id']))
        conn.commit()
        
        # 设置会话
        session['user_id'] = user['id']
        session['username'] = user['username']
        
        return jsonify({'success': True})
    
    except Error as e:
        return jsonify({'success': False, 'message': f'登录失败: {str(e)}'})
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'GET':
        # 如果用户已登录，重定向到主页
        if 'user_id' in session:
            return redirect(url_for('index'))
        return render_template('register.html')
    
    # 处理POST请求（AJAX注册）
    if request.content_type == 'application/json':
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
    else:
        # 处理传统表单提交
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
    
    if not username or not email or not password:
        return jsonify({'success': False, 'message': '所有字段都必须填写'})
    
    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '服务器错误，请稍后再试'})
    
    try:
        cursor = conn.cursor()
        
        # 检查用户名和邮箱是否已被使用
        cursor.execute("SELECT id FROM users WHERE username = %s OR email = %s", (username, email))
        existing_user = cursor.fetchone()
        
        if existing_user:
            return jsonify({'success': False, 'message': '用户名或邮箱已被使用'})
        
        # 创建新用户
        password_hash, salt = hash_password(password)
        
        cursor.execute("""
        INSERT INTO users (username, email, password_hash, salt)
        VALUES (%s, %s, %s, %s)
        """, (username, email, password_hash, salt))
        
        conn.commit()
        
        return jsonify({'success': True})
    
    except Error as e:
        return jsonify({'success': False, 'message': f'注册失败: {str(e)}'})
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

@app.route('/logout')
def logout():
    """用户退出登录"""
    session.pop('user_id', None)
    session.pop('username', None)
    return redirect(url_for('login'))

@app.route('/detect', methods=['POST'])
@login_required
def detect_fake_review():
    data = request.get_json()
    review_text = data.get('review', '')
    
    if not review_text:
        return jsonify({'error': 'No review text provided'}), 400
    
    # Make prediction
    label, probability = predict(model, review_text, preprocessor, device)
    
    result = {
        'is_fake': bool(label),
        'probability': float(probability),
        'prediction': 'FAKE' if label else 'GENUINE'
    }
    
    return jsonify(result)

@app.route('/get_sample', methods=['GET'])
@login_required
def get_sample():
    """Get a random sample from the test set"""
    sample = random.choice(test_samples)
    return jsonify(sample)

if __name__ == '__main__':
    # Load the model and test samples before starting the app
    load_model()
    load_test_samples()
    app.run(debug=True) 