{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 二维斜碰建模演示\n", "\n", "本notebook演示了如何使用二维斜碰模拟器来模拟质点的斜向碰撞。\n", "\n", "## 物理原理\n", "\n", "### 斜碰与正碰的区别\n", "- **正碰**：速度方向沿着连心线，角度为0°或180°\n", "- **斜碰**：速度方向与连心线有夹角，需要考虑速度的分量\n", "\n", "### 斜碰的关键概念\n", "1. **连心线**：碰撞瞬间两球心的连线\n", "2. **法向分量**：速度在连心线方向的分量（参与碰撞）\n", "3. **切向分量**：速度在垂直连心线方向的分量（碰撞时不变）\n", "\n", "### 三种碰撞类型的处理\n", "- **完全弹性斜碰**：法向分量按弹性碰撞公式计算，切向分量不变\n", "- **非弹性斜碰**：法向分量乘以恢复系数e，切向分量不变  \n", "- **完全非弹性斜碰**：法向分量按完全非弹性公式，切向分量不变"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.animation as animation\n", "from IPython.display import HTML\n", "import math\n", "\n", "# 导入我们的模拟器\n", "from oblique_collision_simulator import ObliqueCollisionSimulator\n", "\n", "# 设置matplotlib显示中文\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 示例1：完全弹性斜碰"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建模拟器\n", "sim1 = ObliqueCollisionSimulator(m1=1.0, m2=1.0, dt=0.01, total_time=3.0)\n", "\n", "# 设置初始条件（斜碰场景）\n", "print(\"完全弹性斜碰 - 初始条件:\")\n", "print(\"质点1: 位置(0, 0), 速度(1.2, 0.8), 质量=1.0\")\n", "print(\"质点2: 位置(1.8, 0.5), 速度(-0.9, -0.3), 质量=1.0\")\n", "\n", "sim1.set_initial_conditions(\n", "    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,\n", "    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3\n", ")\n", "\n", "# 运行完全弹性碰撞模拟\n", "sim1.simulate('elastic')\n", "\n", "# 创建动画\n", "ani1 = sim1.create_animation(\"完全弹性斜碰\")\n", "\n", "# 显示动画\n", "HTML(ani1.to_jshtml())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 示例2：非弹性斜碰"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建模拟器\n", "sim2 = ObliqueCollisionSimulator(m1=1.0, m2=1.5, dt=0.01, total_time=3.0)\n", "\n", "# 设置相同的初始条件\n", "print(\"非弹性斜碰 (e=0.6) - 初始条件:\")\n", "print(\"质点1: 位置(0, 0), 速度(1.2, 0.8), 质量=1.0\")\n", "print(\"质点2: 位置(1.8, 0.5), 速度(-0.9, -0.3), 质量=1.5\")\n", "\n", "sim2.set_initial_conditions(\n", "    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,\n", "    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3\n", ")\n", "\n", "# 运行非弹性碰撞模拟（恢复系数e=0.6）\n", "sim2.simulate('inelastic', e=0.6)\n", "\n", "# 创建动画\n", "ani2 = sim2.create_animation(\"非弹性斜碰 (e=0.6)\")\n", "\n", "# 显示动画\n", "HTML(ani2.to_jshtml())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 示例3：完全非弹性斜碰"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建模拟器\n", "sim3 = ObliqueCollisionSimulator(m1=1.0, m2=1.5, dt=0.01, total_time=3.0)\n", "\n", "# 设置相同的初始条件\n", "print(\"完全非弹性斜碰 - 初始条件:\")\n", "print(\"质点1: 位置(0, 0), 速度(1.2, 0.8), 质量=1.0\")\n", "print(\"质点2: 位置(1.8, 0.5), 速度(-0.9, -0.3), 质量=1.5\")\n", "\n", "sim3.set_initial_conditions(\n", "    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,\n", "    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3\n", ")\n", "\n", "# 运行完全非弹性碰撞模拟\n", "sim3.simulate('completely_inelastic')\n", "\n", "# 创建动画\n", "ani3 = sim3.create_animation(\"完全非弹性斜碰\")\n", "\n", "# 显示动画\n", "HTML(ani3.to_jshtml())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 示例4：三种碰撞类型对比"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_comparison_animation():\n", "    \"\"\"创建三种碰撞类型的对比动画\"\"\"\n", "    fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "    \n", "    # 相同的初始条件\n", "    initial_conditions = {\n", "        'x1': 0.0, 'y1': 0.0, 'vx1': 1.0, 'vy1': 0.5,\n", "        'x2': 1.5, 'y2': 0.3, 'vx2': -0.8, 'vy2': -0.2\n", "    }\n", "    \n", "    collision_types = ['elastic', 'inelastic', 'completely_inelastic']\n", "    titles = ['完全弹性碰撞', '非弹性碰撞 (e=0.6)', '完全非弹性碰撞']\n", "    \n", "    simulators = []\n", "    animations_data = []\n", "    \n", "    for i, (collision_type, title) in enumerate(zip(collision_types, titles)):\n", "        # 创建模拟器\n", "        sim = ObliqueCollisionSimulator()\n", "        sim.set_initial_conditions(**initial_conditions)\n", "        \n", "        # 运行模拟\n", "        if collision_type == 'inelastic':\n", "            sim.simulate(collision_type, e=0.6)\n", "        else:\n", "            sim.simulate(collision_type)\n", "            \n", "        simulators.append(sim)\n", "        \n", "        # 设置子图\n", "        ax = axes[i]\n", "        x_min = min(np.min(sim.x1_vals), np.min(sim.x2_vals)) - 0.3\n", "        x_max = max(np.max(sim.x1_vals), np.max(sim.x2_vals)) + 0.3\n", "        y_min = min(np.min(sim.y1_vals), np.min(sim.y2_vals)) - 0.3\n", "        y_max = max(np.max(sim.y1_vals), np.max(sim.y2_vals)) + 0.3\n", "        \n", "        ax.set_xlim(x_min, x_max)\n", "        ax.set_ylim(y_min, y_max)\n", "        ax.set_aspect('equal')\n", "        ax.grid(True, alpha=0.3)\n", "        ax.set_xlabel('X 位置')\n", "        ax.set_ylabel('Y 位置')\n", "        ax.set_title(title)\n", "        \n", "        # 创建质点和轨迹\n", "        atom1, = ax.plot([], [], 'bo', ms=12, label='质点1')\n", "        atom2, = ax.plot([], [], 'ro', ms=12, label='质点2')\n", "        trail1, = ax.plot([], [], 'b-', alpha=0.3, linewidth=1)\n", "        trail2, = ax.plot([], [], 'r-', alpha=0.3, linewidth=1)\n", "        \n", "        if i == 0:  # 只在第一个子图显示图例\n", "            ax.legend()\n", "            \n", "        animations_data.append((atom1, atom2, trail1, trail2, sim))\n", "    \n", "    def init():\n", "        elements = []\n", "        for atom1, atom2, trail1, trail2, _ in animations_data:\n", "            atom1.set_data([], [])\n", "            atom2.set_data([], [])\n", "            trail1.set_data([], [])\n", "            trail2.set_data([], [])\n", "            elements.extend([atom1, atom2, trail1, trail2])\n", "        return elements\n", "    \n", "    def update(frame):\n", "        elements = []\n", "        for atom1, atom2, trail1, trail2, sim in animations_data:\n", "            # 更新质点位置\n", "            atom1.set_data(sim.x1_vals[frame], sim.y1_vals[frame])\n", "            atom2.set_data(sim.x2_vals[frame], sim.y2_vals[frame])\n", "            \n", "            # 更新轨迹\n", "            trail_length = min(frame + 1, 30)\n", "            start_idx = max(0, frame + 1 - trail_length)\n", "            trail1.set_data(sim.x1_vals[start_idx:frame+1], sim.y1_vals[start_idx:frame+1])\n", "            trail2.set_data(sim.x2_vals[start_idx:frame+1], sim.y2_vals[start_idx:frame+1])\n", "            \n", "            elements.extend([atom1, atom2, trail1, trail2])\n", "        return elements\n", "    \n", "    ani = animation.FuncAnimation(fig, update, frames=simulators[0].num_steps,\n", "                                init_func=init, blit=True, interval=50)\n", "    plt.tight_layout()\n", "    return ani\n", "\n", "# 创建对比动画\n", "comparison_ani = create_comparison_animation()\n", "\n", "# 显示动画\n", "HTML(comparison_ani.to_jshtml())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数学模型总结\n", "\n", "### 速度分解\n", "对于斜碰，我们需要将速度分解为法向和切向分量：\n", "\n", "1. **连心线单位向量（法向）**：\n", "   $$\\vec{n} = \\frac{\\vec{r_1} - \\vec{r_2}}{|\\vec{r_1} - \\vec{r_2}|}$$\n", "\n", "2. **切向单位向量**：\n", "   $$\\vec{t} = (-n_y, n_x)$$\n", "\n", "3. **速度分解**：\n", "   - 法向分量：$v_{1n} = \\vec{v_1} \\cdot \\vec{n}$\n", "   - 切向分量：$v_{1t} = \\vec{v_1} \\cdot \\vec{t}$\n", "\n", "### 碰撞计算\n", "只对法向分量应用一维碰撞公式，切向分量保持不变：\n", "\n", "1. **完全弹性碰撞**：\n", "   $$v_{1n}' = \\frac{(m_1-m_2)v_{1n} + 2m_2v_{2n}}{m_1+m_2}$$\n", "   $$v_{2n}' = \\frac{2m_1v_{1n} + (m_2-m_1)v_{2n}}{m_1+m_2}$$\n", "\n", "2. **非弹性碰撞**：\n", "   在弹性碰撞公式基础上乘以恢复系数e\n", "\n", "3. **完全非弹性碰撞**：\n", "   $$v_{1n}' = v_{2n}' = \\frac{m_1v_{1n} + m_2v_{2n}}{m_1+m_2}$$\n", "\n", "### 速度合成\n", "将计算后的法向分量和原切向分量合成新的速度向量：\n", "$$\\vec{v_1}' = v_{1n}'\\vec{n} + v_{1t}\\vec{t}$$"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}