import nltk
import ssl
import os

try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

def create_manual_stopwords():
    """手动创建英文停用词列表"""
    english_stopwords = {
        'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', "you're",
        "you've", "you'll", "you'd", 'your', 'yours', 'yourself', 'yourselves', 'he',
        'him', 'his', 'himself', 'she', "she's", 'her', 'hers', 'herself', 'it', "it's",
        'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which',
        'who', 'whom', 'this', 'that', "that'll", 'these', 'those', 'am', 'is', 'are',
        'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having', 'do', 'does',
        'did', 'doing', 'a', 'an', 'the', 'and', 'but', 'if', 'or', 'because', 'as', 'until',
        'while', 'of', 'at', 'by', 'for', 'with', 'through', 'during', 'before', 'after',
        'above', 'below', 'up', 'down', 'in', 'out', 'on', 'off', 'over', 'under', 'again',
        'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all',
        'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor',
        'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will',
        'just', 'don', "don't", 'should', "should've", 'now', 'd', 'll', 'm', 'o', 're',
        've', 'y', 'ain', 'aren', "aren't", 'couldn', "couldn't", 'didn', "didn't", 'doesn',
        "doesn't", 'hadn', "hadn't", 'hasn', "hasn't", 'haven', "haven't", 'isn', "isn't",
        'ma', 'mightn', "mightn't", 'mustn', "mustn't", 'needn', "needn't", 'shan', "shan't",
        'shouldn', "shouldn't", 'wasn', "wasn't", 'weren', "weren't", 'won', "won't", 'wouldn',
        "wouldn't"
    }
    return english_stopwords

def download_nltk_resources():
    print("开始下载NLTK资源...")

    # 下载基础资源
    resources = [
        'punkt',          # 分词器
        'stopwords',      # 停用词
        'wordnet',        # 词典
        'averaged_perceptron_tagger'  # 词性标注
    ]

    success_count = 0
    for resource in resources:
        print(f"正在下载 {resource}...")
        try:
            nltk.download(resource)
            success_count += 1
        except Exception as e:
            print(f"下载 {resource} 失败: {e}")

    if success_count == 0:
        print("所有NLTK资源下载失败，将使用手动创建的停用词")
        # 创建手动停用词文件
        stopwords_set = create_manual_stopwords()

        # 创建本地停用词文件
        os.makedirs('local_nltk_data', exist_ok=True)
        with open('local_nltk_data/english_stopwords.txt', 'w', encoding='utf-8') as f:
            for word in sorted(stopwords_set):
                f.write(word + '\n')
        print("已创建本地停用词文件: local_nltk_data/english_stopwords.txt")
    else:
        print(f"NLTK资源下载完成！成功下载 {success_count}/{len(resources)} 个资源")

if __name__ == "__main__":
    download_nltk_resources()